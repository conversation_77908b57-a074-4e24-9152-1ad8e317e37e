const mongoose = require('mongoose');

const pageSubmissionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  imageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AdminImage',
    required: true
  },
  submissionContent: {
    type: String,
    required: true
  },
  submissionTimestamp: {
    type: Date,
    default: Date.now
  },
  isReviewed: {
    type: Boolean,
    default: false
  },
  reviewScore: {
    type: Number,
    min: 0,
    max: 100
  },
  reviewComments: {
    type: String
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: {
    type: Date
  },
  status: {
    type: String,
    enum: ['submitted', 'under_review', 'approved', 'rejected'],
    default: 'submitted'
  }
});

// Create compound index for efficient queries
pageSubmissionSchema.index({ userId: 1, imageId: 1 });
pageSubmissionSchema.index({ submissionTimestamp: -1 });
pageSubmissionSchema.index({ status: 1 });

module.exports = mongoose.model('PageSubmission', pageSubmissionSchema);
