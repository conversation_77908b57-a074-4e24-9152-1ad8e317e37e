const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { verifyToken } = require('../middleware/auth');
const {
  validateUserRegistration,
  validateUserLogin,
  validateOTP,
  validateMongoId
} = require('../middleware/validation');

// Public routes
router.post('/register', validateUserRegistration, authController.register);
router.post('/verify-otp', validateOTP, authController.verifyOTP);
router.post('/login', validateUserLogin, authController.login);
router.post('/admin-login', validateUserLogin, authController.adminLogin);
router.get('/agreement', authController.getAgreement);

// Protected routes
router.post('/sign-agreement', verifyToken, authController.signAgreement);
router.get('/get-agreement/:userId', verifyToken, validateMongoId('userId'), authController.getUserSignedAgreement);

module.exports = router;
