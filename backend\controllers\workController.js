const User = require('../models/User');
const Work = require('../models/Work');
const WorkReview = require('../models/WorkReview');
const PageSubmission = require('../models/PageSubmission');
const AdminImage = require('../models/AdminImage');
const nodemailer = require('nodemailer');

// Email transporter setup
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: "<EMAIL>",
    pass: "bpfflehyrjnoojoz"
  }
});

// Send deadline alert email
const sendDeadlineAlert = async (email, name, daysLeft) => {
  const mailOptions = {
    from: "<EMAIL>",
    to: email,
    subject: `Work Deadline Alert - ${daysLeft} days remaining`,
    html: `
      <h2>Work Deadline Alert</h2>
      <p>Dear ${name},</p>
      <p>This is a reminder that you have <strong>${daysLeft} days left</strong> to complete and submit your work.</p>
      <p>Please log in to your dashboard to continue working on your assignment.</p>
      <p>Best regards,<br>The Team</p>
    `
  };

  await transporter.sendMail(mailOptions);
};

// Send work start notification email to admin
const sendWorkStartNotificationEmail = async (workData) => {
  const { userName, userEmail, projectLink, password, timestamp } = workData;

  const mailOptions = {
    from: "<EMAIL>",
    to: "<EMAIL>", // Admin email
    subject: `🚀 Work Started - ${userName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4F46E5;">🚀 New Work Started</h2>
        <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">User Details:</h3>
          <p><strong>Name:</strong> ${userName}</p>
          <p><strong>Email:</strong> ${userEmail}</p>
          <p><strong>Started At:</strong> ${timestamp.toLocaleString()}</p>
        </div>
        <div style="background-color: #EFF6FF; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Project Details:</h3>
          <p><strong>Project Link:</strong> <a href="${projectLink}" target="_blank">${projectLink}</a></p>
          <p><strong>Password:</strong> <code style="background-color: #FEF3C7; padding: 2px 6px; border-radius: 4px;">${password}</code></p>
        </div>
        <div style="background-color: #FEF2F2; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #DC2626;"><strong>⏰ Reminder:</strong> User has 4 days to complete the work. Automatic penalty will be applied after deadline.</p>
        </div>
      </div>
    `
  };

  await transporter.sendMail(mailOptions);
};

// Start work
exports.startWork = async (req, res) => {
  try {
    const { projectLink, password } = req.body;
    const user = await User.findById(req.user.userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    if (user.workStartedAt) {
      return res.status(400).json({ message: 'Work already started' });
    }

    if (!projectLink || !password) {
      return res.status(400).json({ message: 'Project link and password are required' });
    }

    const startTimestamp = new Date();

    // Set work start time and project details
    user.workStartedAt = startTimestamp;
    user.projectLink = projectLink;
    await user.save();

    // Create or get work document
    let work = await Work.findOne({ userId: req.user.userId });
    if (!work) {
      work = new Work({
        userId: req.user.userId,
        startedAt: startTimestamp
      });
      await work.save();
    }

    // Send real-time notification to admin
    const io = req.app.get('io');
    if (io) {
      io.to('admin-room').emit('work-started', {
        userId: user._id,
        userName: user.name,
        userEmail: user.email,
        projectLink,
        password,
        timestamp: startTimestamp
      });
    }

    // Send email notification to admin
    try {
      await sendWorkStartNotificationEmail({
        userId: user._id,
        userName: user.name,
        userEmail: user.email,
        projectLink,
        password,
        timestamp: startTimestamp
      });
    } catch (emailError) {
      console.error('Failed to send admin notification email:', emailError.message);
    }

    res.status(200).json({
      message: 'Work started successfully',
      work,
      startedAt: user.workStartedAt,
      projectLink: user.projectLink
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Save work draft
exports.saveDraft = async (req, res) => {
  try {
    const { content } = req.body;

    let work = await Work.findOne({ userId: req.user.userId });

    if (!work) {
      return res.status(404).json({ message: 'Work not found. Please start work first.' });
    }

    work.content = content;
    work.lastSaved = new Date();
    await work.save();

    res.status(200).json({
      message: 'Draft saved successfully',
      work
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get work draft
exports.getDraft = async (req, res) => {
  try {
    const work = await Work.findOne({ userId: req.user.userId });

    if (!work) {
      return res.status(404).json({ message: 'No work found' });
    }

    res.status(200).json({ work });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Submit work
exports.submitWork = async (req, res) => {
  try {
    const { content } = req.body;

    const user = await User.findById(req.user.userId);
    const work = await Work.findOne({ userId: req.user.userId });

    if (!user || !work) {
      return res.status(404).json({ message: 'User or work not found' });
    }

    if (user.workSubmitted) {
      return res.status(400).json({ message: 'Work already submitted' });
    }

    // Check if deadline passed
    const startDate = new Date(user.workStartedAt);
    const currentDate = new Date();
    const deadlineDate = new Date(startDate.getTime() + (4 * 24 * 60 * 60 * 1000));

    if (currentDate > deadlineDate) {
      user.isPenalized = true;
    }

    const submissionTime = new Date();

    // Update work and user
    work.content = content;
    work.isSubmitted = true;
    work.submittedAt = submissionTime;
    await work.save();

    user.workSubmitted = true;
    await user.save();

    // Create work review record
    const workReview = new WorkReview({
      userId: user._id,
      workId: work._id,
      submittedAt: submissionTime,
      autoReviewScheduledAt: new Date(submissionTime.getTime() + (24 * 60 * 60 * 1000))
    });
    await workReview.save();

    // Schedule 24-hour penalty check
    const { scheduleAutoPenalty } = require('../services/automationService');
    scheduleAutoPenalty(user._id, submissionTime);

    // Schedule automatic accuracy assignment after 24 hours
    scheduleAccuracyAssignment(user._id, submissionTime);

    res.status(200).json({
      message: 'Work submitted successfully! Your work will be reviewed in 24 hours.',
      work,
      isPenalized: user.isPenalized,
      submittedAt: submissionTime,
      reviewDeadline: new Date(submissionTime.getTime() + (24 * 60 * 60 * 1000))
    });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Check deadline and send alerts (to be called by cron job)
exports.checkDeadlines = async (req, res) => {
  try {
    const users = await User.find({
      workStartedAt: { $exists: true },
      workSubmitted: false,
      isPenalized: false
    });

    const currentDate = new Date();

    for (const user of users) {
      const startDate = new Date(user.workStartedAt);
      const deadlineDate = new Date(startDate.getTime() + (4 * 24 * 60 * 60 * 1000));
      const timeRemaining = deadlineDate - currentDate;
      const daysLeft = Math.ceil(timeRemaining / (24 * 60 * 60 * 1000));

      // Send alerts for 3, 2, 1 days left
      if (daysLeft === 3 || daysLeft === 2 || daysLeft === 1) {
        await sendDeadlineAlert(user.email, user.name, daysLeft);
      }

      // Auto-penalize if deadline passed
      if (daysLeft <= 0) {
        user.isPenalized = true;
        await user.save();
      }
    }

    res.status(200).json({ message: 'Deadline check completed' });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Schedule accuracy assignment after 24 hours
const scheduleAccuracyAssignment = (userId, submissionTime) => {
  const reviewTime = new Date(submissionTime.getTime() + (24 * 60 * 60 * 1000));

  setTimeout(async () => {
    try {
      await assignAccuracyResult(userId);
    } catch (error) {
      console.error('Error assigning accuracy result:', error);
    }
  }, reviewTime.getTime() - Date.now());
};

// Assign random accuracy result
const assignAccuracyResult = async (userId) => {
  try {
    const user = await User.findById(userId);
    const workReview = await WorkReview.findOne({ userId, isReviewed: false });

    if (!user || !workReview) {
      console.log('User or work review not found for accuracy assignment');
      return;
    }

    // Predefined accuracy results
    const accuracyResults = [
      { correct: 172, wrong: 28, percentage: 86 },
      { correct: 164, wrong: 36, percentage: 82 },
      { correct: 174, wrong: 26, percentage: 87 },
      { correct: 161, wrong: 38, percentage: 81 },
      { correct: 176, wrong: 24, percentage: 88 }
    ];

    // Randomly select one result
    const randomResult = accuracyResults[Math.floor(Math.random() * accuracyResults.length)];
    const reviewedAt = new Date();

    // Update work review
    workReview.accuracyResult = randomResult;
    workReview.isReviewed = true;
    workReview.reviewedAt = reviewedAt;
    workReview.reviewStatus = 'completed';
    await workReview.save();

    // Update user with accuracy result
    user.accuracyResult = {
      ...randomResult,
      reviewedAt
    };
    user.hasAccuracyResult = true;
    await user.save();

    console.log(`Accuracy result assigned to user ${user.name}: ${randomResult.percentage}%`);
  } catch (error) {
    console.error('Error in assignAccuracyResult:', error);
  }
};

// Save page work draft
exports.savePageDraft = async (req, res) => {
  try {
    const { imageId, submissionContent } = req.body;
    const userId = req.user.userId;

    if (!imageId) {
      return res.status(400).json({ message: 'Image ID is required' });
    }

    // Check if image exists and is active
    const image = await AdminImage.findById(imageId);
    if (!image || !image.isActive) {
      return res.status(404).json({ message: 'Image not found or not active' });
    }

    // Find existing draft or create new one
    let pageSubmission = await PageSubmission.findOne({
      userId,
      imageId,
      isDraft: true
    });

    if (pageSubmission) {
      // Update existing draft
      pageSubmission.submissionContent = submissionContent || '';
      pageSubmission.lastSaved = new Date();
    } else {
      // Create new draft
      pageSubmission = new PageSubmission({
        userId,
        imageId,
        submissionContent: submissionContent || '',
        isDraft: true,
        lastSaved: new Date(),
        status: 'draft'
      });
    }

    await pageSubmission.save();

    res.status(200).json({
      message: 'Draft saved successfully',
      draft: pageSubmission
    });
  } catch (error) {
    console.error('Save page draft error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get page work draft
exports.getPageDraft = async (req, res) => {
  try {
    const { imageId } = req.params;
    const userId = req.user.userId;

    if (!imageId) {
      return res.status(400).json({ message: 'Image ID is required' });
    }

    // Check if image exists and is active
    const image = await AdminImage.findById(imageId);
    if (!image || !image.isActive) {
      return res.status(404).json({ message: 'Image not found or not active' });
    }

    // Find draft
    const draft = await PageSubmission.findOne({
      userId,
      imageId,
      isDraft: true
    });

    res.status(200).json({
      draft: draft || null,
      image
    });
  } catch (error) {
    console.error('Get page draft error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Submit work for individual page
exports.submitPageWork = async (req, res) => {
  try {
    const { imageId, submissionContent } = req.body;
    const userId = req.user.userId;

    if (!imageId || !submissionContent) {
      return res.status(400).json({ message: 'Image ID and submission content are required' });
    }

    // Check if image exists and is active
    const image = await AdminImage.findById(imageId);
    if (!image || !image.isActive) {
      return res.status(404).json({ message: 'Image not found or not active' });
    }

    // Check if user already submitted work for this image (not draft)
    const existingSubmission = await PageSubmission.findOne({
      userId,
      imageId,
      isDraft: false
    });
    if (existingSubmission) {
      return res.status(400).json({ message: 'Work already submitted for this image' });
    }

    // Check if there's an existing draft and convert it to submission
    let pageSubmission = await PageSubmission.findOne({
      userId,
      imageId,
      isDraft: true
    });

    if (pageSubmission) {
      // Convert draft to submission
      pageSubmission.submissionContent = submissionContent;
      pageSubmission.isDraft = false;
      pageSubmission.submissionTimestamp = new Date();
      pageSubmission.status = 'submitted';
    } else {
      // Create new page submission
      pageSubmission = new PageSubmission({
        userId,
        imageId,
        submissionContent,
        isDraft: false,
        submissionTimestamp: new Date(),
        status: 'submitted'
      });
    }

    await pageSubmission.save();

    res.status(201).json({
      message: 'Work submitted successfully',
      submission: pageSubmission
    });
  } catch (error) {
    console.error('Submit page work error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user's page submissions
exports.getUserPageSubmissions = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { includeDrafts } = req.query;

    const query = { userId };
    if (!includeDrafts || includeDrafts === 'false') {
      query.isDraft = false; // Only get submitted work, not drafts
    }

    const submissions = await PageSubmission.find(query)
      .populate('imageId', 'title description filename')
      .sort({ submissionTimestamp: -1, lastSaved: -1 });

    res.status(200).json({ submissions });
  } catch (error) {
    console.error('Get user page submissions error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get user's submission progress
exports.getUserProgress = async (req, res) => {
  try {
    const userId = req.user.userId;

    // Get total active images
    const totalImages = await AdminImage.countDocuments({ isActive: true });

    // Get user's submissions count (only submitted, not drafts)
    const submittedCount = await PageSubmission.countDocuments({
      userId,
      isDraft: false
    });

    // Get list of submitted image IDs (only submitted, not drafts)
    const submittedImages = await PageSubmission.find({
      userId,
      isDraft: false
    }).select('imageId');
    const submittedImageIds = submittedImages.map(sub => sub.imageId.toString());

    // Get drafts count
    const draftsCount = await PageSubmission.countDocuments({
      userId,
      isDraft: true
    });

    // Get list of draft image IDs
    const draftImages = await PageSubmission.find({
      userId,
      isDraft: true
    }).select('imageId');
    const draftImageIds = draftImages.map(draft => draft.imageId.toString());

    res.status(200).json({
      totalImages,
      submittedCount,
      submittedImageIds,
      draftsCount,
      draftImageIds,
      progress: totalImages > 0 ? Math.round((submittedCount / totalImages) * 100) : 0
    });
  } catch (error) {
    console.error('Get user progress error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  startWork: exports.startWork,
  saveDraft: exports.saveDraft,
  getDraft: exports.getDraft,
  submitWork: exports.submitWork,
  checkDeadlines: exports.checkDeadlines,
  assignAccuracyResult,
  savePageDraft: exports.savePageDraft,
  getPageDraft: exports.getPageDraft,
  submitPageWork: exports.submitPageWork,
  getUserPageSubmissions: exports.getUserPageSubmissions,
  getUserProgress: exports.getUserProgress
};
