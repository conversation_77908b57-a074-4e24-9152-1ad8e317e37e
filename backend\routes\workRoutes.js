const express = require('express');
const router = express.Router();
const workController = require('../controllers/workController');
const { verifyToken, isVerifiedAndSigned } = require('../middleware/auth');
const {
  validateWorkStart,
  validateWorkDraft,
  validateWorkSubmission,
  validatePageWorkDraft,
  validatePageWorkSubmission,
  validateMongoId
} = require('../middleware/validation');

// Public PDF download route (no authentication required)
router.get('/pdfs/:pdfId/download', async (req, res) => {
  try {
    const AdminPDF = require('../models/AdminPDF');
    const path = require('path');
    const fs = require('fs').promises;

    const { pdfId } = req.params;

    // Check if PDF exists and is active
    const pdf = await AdminPDF.findById(pdfId);

    if (!pdf) {
      return res.status(404).json({ message: 'PDF not found' });
    }

    if (!pdf.isActive) {
      return res.status(403).json({ message: 'PDF is not available for download' });
    }

    const filePath = path.join(__dirname, '../userpdf/', pdf.filename);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch (error) {
      return res.status(404).json({ message: 'PDF file not found on server' });
    }

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${pdf.originalName}"`);
    res.sendFile(filePath);
  } catch (error) {
    console.error('Download PDF error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// All other work routes require authentication and verification
router.use(verifyToken);
router.use(isVerifiedAndSigned);

// Work management routes
router.post('/start', validateWorkStart, workController.startWork);
router.post('/save-draft', validateWorkDraft, workController.saveDraft);
router.get('/draft', workController.getDraft);
router.post('/submit', validateWorkSubmission, workController.submitWork);

// Get active images for users
router.get('/images', async (req, res) => {
  try {
    const AdminImage = require('../models/AdminImage');
    const images = await AdminImage.find({ isActive: true })
      .select('title description filename originalName fileSize mimeType uploadedAt')
      .sort({ uploadedAt: -1 });

    res.status(200).json({ images });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Serve images statically
router.get('/images/:filename', (req, res) => {
  const { filename } = req.params;
  const path = require('path');
  const filePath = path.join(__dirname, '../userimages/', filename);
  res.sendFile(filePath);
});

// Download image for authenticated users
router.get('/images/:imageId/download', validateMongoId('imageId'), async (req, res) => {
  try {
    const { imageId } = req.params;
    const AdminImage = require('../models/AdminImage');
    const path = require('path');
    const fs = require('fs').promises;

    // Find the image
    const image = await AdminImage.findById(imageId);
    if (!image || !image.isActive) {
      return res.status(404).json({ message: 'Image not found or not active' });
    }

    const filePath = path.join(__dirname, '../userimages/', image.filename);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch (error) {
      return res.status(404).json({ message: 'Image file not found on server' });
    }

    // Set headers for download
    res.setHeader('Content-Type', image.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${image.originalName}"`);
    res.sendFile(filePath);
  } catch (error) {
    console.error('Download image error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Page submission routes
router.post('/save-page-draft', validatePageWorkDraft, workController.savePageDraft);
router.get('/page-draft/:imageId', validateMongoId('imageId'), workController.getPageDraft);
router.post('/submit-page', validatePageWorkSubmission, workController.submitPageWork);
router.get('/page-submissions', workController.getUserPageSubmissions);
router.get('/progress', workController.getUserProgress);

// Admin route for deadline checking (can be called by cron job)
router.post('/check-deadlines', workController.checkDeadlines);

module.exports = router;
