const express = require('express');
const router = express.Router();
const workController = require('../controllers/workController');
const { verifyToken, isVerifiedAndSigned } = require('../middleware/auth');

// Public PDF download route (no authentication required)
router.get('/pdfs/:pdfId/download', async (req, res) => {
  try {
    const AdminPDF = require('../models/AdminPDF');
    const path = require('path');
    const fs = require('fs').promises;

    const { pdfId } = req.params;

    // Check if PDF exists and is active
    const pdf = await AdminPDF.findById(pdfId);

    if (!pdf) {
      return res.status(404).json({ message: 'PDF not found' });
    }

    if (!pdf.isActive) {
      return res.status(403).json({ message: 'PDF is not available for download' });
    }

    const filePath = path.join(__dirname, '../userpdf/', pdf.filename);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch (error) {
      return res.status(404).json({ message: 'PDF file not found on server' });
    }

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${pdf.originalName}"`);
    res.sendFile(filePath);
  } catch (error) {
    console.error('Download PDF error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// All other work routes require authentication and verification
router.use(verifyToken);
router.use(isVerifiedAndSigned);

// Work management routes
router.post('/start', workController.startWork);
router.post('/save-draft', workController.saveDraft);
router.get('/draft', workController.getDraft);
router.post('/submit', workController.submitWork);

// Get active images for users
router.get('/images', async (req, res) => {
  try {
    const AdminImage = require('../models/AdminImage');
    const images = await AdminImage.find({ isActive: true })
      .select('title description filename originalName fileSize mimeType uploadedAt')
      .sort({ uploadedAt: -1 });

    res.status(200).json({ images });
  } catch (error) {
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Serve images statically
router.get('/images/:filename', (req, res) => {
  const { filename } = req.params;
  const path = require('path');
  const filePath = path.join(__dirname, '../userimages/', filename);
  res.sendFile(filePath);
});

// Page submission routes
router.post('/submit-page', workController.submitPageWork);
router.get('/page-submissions', workController.getUserPageSubmissions);
router.get('/progress', workController.getUserProgress);

// Admin route for deadline checking (can be called by cron job)
router.post('/check-deadlines', workController.checkDeadlines);

module.exports = router;
