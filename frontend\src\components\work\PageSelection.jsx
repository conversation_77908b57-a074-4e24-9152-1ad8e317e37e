import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const PageSelection = () => {
  const [images, setImages] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submittedImageIds, setSubmittedImageIds] = useState([]);
  const [draftImageIds, setDraftImageIds] = useState([]);
  const [progress, setProgress] = useState({ submitted: 0, total: 0, percentage: 0, drafts: 0 });
  const navigate = useNavigate();

  useEffect(() => {
    fetchImages();
    fetchProgress();
  }, []);

  const fetchImages = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/work/images`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true
        }
      );
      setImages(response.data.images || []);
    } catch (error) {
      console.error('Error fetching images:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProgress = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/work/progress`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true
        }
      );
      const {
        totalImages,
        submittedCount,
        submittedImageIds,
        draftsCount,
        draftImageIds,
        progress: progressPercentage
      } = response.data;
      setProgress({
        submitted: submittedCount,
        total: totalImages,
        percentage: progressPercentage,
        drafts: draftsCount || 0
      });
      setSubmittedImageIds(submittedImageIds);
      setDraftImageIds(draftImageIds || []);
    } catch (error) {
      console.error('Error fetching progress:', error);
    }
  };

  const handleImageSelect = (imageId) => {
    if (submittedImageIds.includes(imageId)) return; // Can't select already submitted images
    
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    );
  };

  const handleSelectAll = () => {
    const availableImages = images
      .filter(img => !submittedImageIds.includes(img._id))
      .map(img => img._id);
    
    if (selectedImages.length === availableImages.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(availableImages);
    }
  };

  const handleStartWork = () => {
    if (selectedImages.length === 0) {
      alert('Please select at least one image to work on');
      return;
    }

    // Store selected images in localStorage for the work session
    localStorage.setItem('selectedImages', JSON.stringify(selectedImages));
    localStorage.setItem('currentImageIndex', '0');

    navigate('/work-session');
  };

  const handleWorkOnImage = (imageId) => {
    // Navigate directly to the image work editor
    navigate(`/image-work/${imageId}`);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const availableImages = images.filter(img => !submittedImageIds.includes(img._id));

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Select Pages to Work On</h1>
              <p className="text-gray-600">Choose the images you want to work on in this session</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Progress: {progress.submitted}/{progress.total} ({progress.percentage}%)
                {progress.drafts > 0 && (
                  <span className="ml-2 text-yellow-600">• {progress.drafts} drafts</span>
                )}
              </div>
              <button
                onClick={() => navigate('/dashboard')}
                className="px-4 py-2 text-indigo-600 hover:text-indigo-800"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Overall Progress</span>
            <span className="text-sm text-gray-500">{progress.percentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.percentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Selection Controls */}
          <div className="bg-white shadow rounded-lg p-6 mb-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Available Images ({availableImages.length})
                </h3>
                <p className="text-sm text-gray-500">
                  Selected: {selectedImages.length} images
                </p>
              </div>
              <div className="flex space-x-4">
                <button
                  onClick={handleSelectAll}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  {selectedImages.length === availableImages.length ? 'Deselect All' : 'Select All'}
                </button>
                <button
                  onClick={handleStartWork}
                  disabled={selectedImages.length === 0}
                  className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Start Work ({selectedImages.length} selected)
                </button>
              </div>
            </div>
          </div>

          {/* Images Grid */}
          <div className="bg-white shadow rounded-lg p-6">
            {availableImages.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500">
                  <div className="text-4xl mb-4">🎉</div>
                  <h3 className="text-lg font-medium">All images completed!</h3>
                  <p className="text-sm">You have submitted work for all available images.</p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {images.map((image) => {
                  const isSubmitted = submittedImageIds.includes(image._id);
                  const hasDraft = draftImageIds.includes(image._id);
                  const isSelected = selectedImages.includes(image._id);

                  return (
                    <div
                      key={image._id}
                      className={`border-2 rounded-lg p-4 transition-all ${
                        isSubmitted
                          ? 'border-green-300 bg-green-50'
                          : hasDraft
                          ? 'border-yellow-300 bg-yellow-50'
                          : isSelected
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="aspect-w-16 aspect-h-9 mb-4">
                        <img
                          src={`${import.meta.env.VITE_API_URL}/userimages/${image.filename}`}
                          alt={image.title}
                          className="w-full h-48 object-cover rounded"
                        />
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium text-gray-900">{image.title}</h4>
                        <p className="text-sm text-gray-500">{image.description}</p>
                        <div className="text-xs text-gray-400">
                          {formatFileSize(image.fileSize)} • {new Date(image.uploadedAt).toLocaleDateString()}
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="flex flex-col space-y-1">
                            {isSubmitted ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ✓ Completed
                              </span>
                            ) : hasDraft ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                📝 Draft Saved
                              </span>
                            ) : isSelected ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                ✓ Selected
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                Available
                              </span>
                            )}
                          </div>

                          <div className="flex flex-col space-y-1">
                            {!isSubmitted && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleWorkOnImage(image._id);
                                }}
                                className="text-xs bg-indigo-600 text-white px-2 py-1 rounded hover:bg-indigo-700"
                              >
                                {hasDraft ? 'Continue' : 'Work'}
                              </button>
                            )}
                            {!isSubmitted && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleImageSelect(image._id);
                                }}
                                className={`text-xs px-2 py-1 rounded border ${
                                  isSelected
                                    ? 'bg-indigo-100 text-indigo-700 border-indigo-300'
                                    : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                                }`}
                              >
                                {isSelected ? 'Deselect' : 'Select'}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default PageSelection;
