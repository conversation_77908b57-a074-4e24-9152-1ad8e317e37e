import { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import QuillEditor from '../common/QuillEditor';
import axios from 'axios';

const ImageWorkEditor = () => {
  const { imageId } = useParams();
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [currentImage, setCurrentImage] = useState(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const navigate = useNavigate();
  const autoSaveRef = useRef();

  useEffect(() => {
    if (imageId) {
      loadImageAndDraft();
      setupAutoSave();
    }

    return () => {
      if (autoSaveRef.current) {
        clearInterval(autoSaveRef.current);
      }
    };
  }, [imageId]);

  useEffect(() => {
    // Auto-save when content changes
    if (content && !isSubmitted) {
      const timeoutId = setTimeout(() => {
        saveDraft(false); // Save without showing message
      }, 2000); // Save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [content, isSubmitted]);

  const setupAutoSave = () => {
    // Auto-save every 30 seconds
    autoSaveRef.current = setInterval(() => {
      if (content && !isSubmitted) {
        saveDraft(false);
      }
    }, 30000);
  };

  const loadImageAndDraft = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      // Load draft and image info
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/work/page-draft/${imageId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true
        }
      );

      setCurrentImage(response.data.image);
      
      if (response.data.draft) {
        setContent(response.data.draft.submissionContent || '');
        setLastSaved(response.data.draft.lastSaved);
        setIsSubmitted(!response.data.draft.isDraft);
      }
    } catch (error) {
      console.error('Failed to load image and draft:', error);
      alert('Failed to load work data: ' + (error.response?.data?.message || error.message));
      navigate('/page-selection');
    } finally {
      setLoading(false);
    }
  };

  const saveDraft = async (showMessage = true) => {
    if (isSubmitted) return;

    try {
      setSaving(true);
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/work/save-page-draft`,
        { 
          imageId,
          submissionContent: content 
        },
        { 
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true 
        }
      );

      setLastSaved(response.data.draft.lastSaved);
      if (showMessage) {
        alert('Draft saved successfully!');
      }
    } catch (error) {
      console.error('Failed to save draft:', error);
      if (showMessage) {
        alert('Failed to save draft: ' + (error.response?.data?.message || error.message));
      }
    } finally {
      setSaving(false);
    }
  };

  const submitWork = async () => {
    if (!content.trim()) {
      alert('Please enter your work content before submitting.');
      return;
    }

    if (!confirm('Are you sure you want to submit this work? You cannot edit it after submission.')) {
      return;
    }

    try {
      setSubmitting(true);
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/api/work/submit-page`,
        { 
          imageId,
          submissionContent: content 
        },
        { 
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true 
        }
      );

      alert('Work submitted successfully!');
      setIsSubmitted(true);
      
      // Navigate back to page selection
      navigate('/page-selection', { 
        state: { message: 'Work submitted successfully!' }
      });
    } catch (error) {
      console.error('Failed to submit work:', error);
      alert('Failed to submit work: ' + (error.response?.data?.message || error.message));
    } finally {
      setSubmitting(false);
    }
  };

  const downloadImage = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/work/images/${imageId}/download`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true,
          responseType: 'blob'
        }
      );

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', currentImage?.originalName || 'image.jpg');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download image:', error);
      alert('Failed to download image: ' + (error.response?.data?.message || error.message));
    }
  };

  const formatLastSaved = (timestamp) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading image work editor...</p>
        </div>
      </div>
    );
  }

  if (!currentImage) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Image Not Found</h2>
          <p className="text-gray-600 mb-4">The requested image could not be loaded.</p>
          <button
            onClick={() => navigate('/page-selection')}
            className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700"
          >
            Back to Page Selection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/page-selection')}
                className="mr-4 text-gray-500 hover:text-gray-700"
              >
                ← Back
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Image Work Editor</h1>
                <p className="text-sm text-gray-600 mt-1">{currentImage.title}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Auto-save status */}
              <div className="text-sm text-gray-500">
                {saving ? (
                  <span className="text-blue-600">💾 Saving...</span>
                ) : (
                  <span>Last saved: {formatLastSaved(lastSaved)}</span>
                )}
              </div>

              {/* Action buttons */}
              {!isSubmitted && (
                <>
                  <button
                    onClick={() => saveDraft(true)}
                    disabled={saving}
                    className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 disabled:opacity-50"
                  >
                    {saving ? 'Saving...' : 'Save Draft'}
                  </button>
                  <button
                    onClick={submitWork}
                    disabled={submitting || !content.trim()}
                    className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
                  >
                    {submitting ? 'Submitting...' : 'Submit Work'}
                  </button>
                </>
              )}
              
              {isSubmitted && (
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                  ✅ Submitted
                </span>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex gap-6">
            {/* Image Display */}
            <div className="w-1/2 bg-white shadow rounded-lg">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{currentImage.title}</h3>
                  <button
                    onClick={downloadImage}
                    className="bg-indigo-600 text-white px-3 py-1 rounded text-sm hover:bg-indigo-700"
                  >
                    📥 Download Image
                  </button>
                </div>
                
                <div className="mb-4">
                  <img
                    src={`${import.meta.env.VITE_API_URL}/userimages/${currentImage.filename}`}
                    alt={currentImage.title}
                    className="w-full h-auto max-h-96 object-contain border rounded"
                  />
                </div>
                
                {currentImage.description && (
                  <p className="text-sm text-gray-600 mb-4">{currentImage.description}</p>
                )}
                
                <div className="text-xs text-gray-400">
                  Uploaded: {new Date(currentImage.uploadedAt).toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* Editor */}
            <div className="w-1/2 bg-white shadow rounded-lg">
              <div className="p-6">
                {isSubmitted && (
                  <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                    This work has been submitted and is now read-only.
                  </div>
                )}

                <QuillEditor
                  value={content}
                  onChange={setContent}
                  readOnly={isSubmitted}
                  style={{ height: '500px', marginBottom: '50px' }}
                  placeholder={isSubmitted ? '' : 'Describe what you see in the image...'}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ImageWorkEditor;
