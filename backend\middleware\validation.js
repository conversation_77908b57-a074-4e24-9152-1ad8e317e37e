const { body, param, query, validationResult } = require('express-validator');

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }
    next();
  } catch (error) {
    console.error('Validation middleware error:', error);
    res.status(500).json({ 
      message: 'Server error during validation', 
      error: error.message 
    });
  }
};

// Work validation rules
const validateWorkStart = [
  body('projectLink')
    .notEmpty()
    .withMessage('Project link is required')
    .isURL()
    .withMessage('Project link must be a valid URL'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 1 })
    .withMessage('Password cannot be empty'),
  handleValidationErrors
];

const validateWorkDraft = [
  body('content')
    .optional()
    .isString()
    .withMessage('Content must be a string'),
  handleValidationErrors
];

const validateWorkSubmission = [
  body('content')
    .notEmpty()
    .withMessage('Content is required for submission')
    .isString()
    .withMessage('Content must be a string'),
  handleValidationErrors
];

// Page work validation rules
const validatePageWorkDraft = [
  body('imageId')
    .notEmpty()
    .withMessage('Image ID is required')
    .isMongoId()
    .withMessage('Image ID must be a valid MongoDB ObjectId'),
  body('submissionContent')
    .optional()
    .isString()
    .withMessage('Submission content must be a string'),
  handleValidationErrors
];

const validatePageWorkSubmission = [
  body('imageId')
    .notEmpty()
    .withMessage('Image ID is required')
    .isMongoId()
    .withMessage('Image ID must be a valid MongoDB ObjectId'),
  body('submissionContent')
    .notEmpty()
    .withMessage('Submission content is required')
    .isString()
    .withMessage('Submission content must be a string'),
  handleValidationErrors
];

// User validation rules
const validateUserRegistration = [
  body('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('mobileNumber')
    .notEmpty()
    .withMessage('Mobile number is required')
    .isMobilePhone()
    .withMessage('Valid mobile number is required'),
  body('address')
    .notEmpty()
    .withMessage('Address is required')
    .isLength({ min: 5, max: 200 })
    .withMessage('Address must be between 5 and 200 characters'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

const validateOTP = [
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  body('otp')
    .notEmpty()
    .withMessage('OTP is required')
    .isLength({ min: 6, max: 6 })
    .withMessage('OTP must be 6 digits'),
  handleValidationErrors
];

// Admin validation rules
const validateImageUpload = [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Title must be between 1 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  handleValidationErrors
];

// Parameter validation
const validateMongoId = (paramName) => [
  param(paramName)
    .isMongoId()
    .withMessage(`${paramName} must be a valid MongoDB ObjectId`),
  handleValidationErrors
];

// Query validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateWorkStart,
  validateWorkDraft,
  validateWorkSubmission,
  validatePageWorkDraft,
  validatePageWorkSubmission,
  validateUserRegistration,
  validateUserLogin,
  validateOTP,
  validateImageUpload,
  validateMongoId,
  validatePagination
};
