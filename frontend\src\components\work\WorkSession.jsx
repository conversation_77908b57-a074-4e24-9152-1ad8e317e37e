import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const WorkSession = () => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    initializeWorkSession();
  }, []);

  useEffect(() => {
    if (selectedImages.length > 0 && currentImageIndex < selectedImages.length) {
      // Navigate to the new image work editor instead of handling inline
      const currentImageId = selectedImages[currentImageIndex];
      navigate(`/image-work/${currentImageId}`);
    }
  }, [selectedImages, currentImageIndex, navigate]);

  const initializeWorkSession = () => {
    const storedImages = localStorage.getItem('selectedImages');
    const storedIndex = localStorage.getItem('currentImageIndex');
    
    if (!storedImages) {
      navigate('/page-selection');
      return;
    }
    
    const images = JSON.parse(storedImages);
    const index = parseInt(storedIndex) || 0;
    
    setSelectedImages(images);
    setCurrentImageIndex(index);
    setLoading(false);
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting to image work editor...</p>
        </div>
      </div>
    );
  }

  // This component now just redirects to the ImageWorkEditor
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Redirecting to image work editor...</p>
      </div>
    </div>
  );
};

export default WorkSession;
