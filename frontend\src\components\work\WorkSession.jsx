import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const WorkSession = () => {
  const [selectedImages, setSelectedImages] = useState([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentImage, setCurrentImage] = useState(null);
  const [submissionContent, setSubmissionContent] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    initializeWorkSession();
  }, []);

  useEffect(() => {
    if (selectedImages.length > 0 && currentImageIndex < selectedImages.length) {
      fetchCurrentImage();
    }
  }, [selectedImages, currentImageIndex]);

  const initializeWorkSession = () => {
    const storedImages = localStorage.getItem('selectedImages');
    const storedIndex = localStorage.getItem('currentImageIndex');
    
    if (!storedImages) {
      navigate('/page-selection');
      return;
    }
    
    const images = JSON.parse(storedImages);
    const index = parseInt(storedIndex) || 0;
    
    setSelectedImages(images);
    setCurrentImageIndex(index);
    setLoading(false);
  };

  const fetchCurrentImage = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(
        `${import.meta.env.VITE_API_URL}/api/work/images`,
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true
        }
      );
      
      const images = response.data.images || [];
      const currentImageId = selectedImages[currentImageIndex];
      const image = images.find(img => img._id === currentImageId);
      
      setCurrentImage(image);
    } catch (error) {
      console.error('Error fetching current image:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!submissionContent.trim()) {
      alert('Please enter your work content');
      return;
    }
    
    setSubmitting(true);
    
    try {
      const token = localStorage.getItem('token');
      await axios.post(
        `${import.meta.env.VITE_API_URL}/api/work/submit-page`,
        {
          imageId: currentImage._id,
          submissionContent: submissionContent.trim()
        },
        {
          headers: { Authorization: `Bearer ${token}` },
          withCredentials: true
        }
      );
      
      alert('Work submitted successfully!');
      
      // Move to next image or complete session
      const nextIndex = currentImageIndex + 1;
      if (nextIndex < selectedImages.length) {
        setCurrentImageIndex(nextIndex);
        setSubmissionContent('');
        localStorage.setItem('currentImageIndex', nextIndex.toString());
      } else {
        // Session complete
        localStorage.removeItem('selectedImages');
        localStorage.removeItem('currentImageIndex');
        navigate('/page-selection', { 
          state: { message: 'Work session completed successfully!' }
        });
      }
    } catch (error) {
      console.error('Submit error:', error);
      alert(error.response?.data?.message || 'Failed to submit work');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSkip = () => {
    const nextIndex = currentImageIndex + 1;
    if (nextIndex < selectedImages.length) {
      setCurrentImageIndex(nextIndex);
      setSubmissionContent('');
      localStorage.setItem('currentImageIndex', nextIndex.toString());
    } else {
      // Session complete
      localStorage.removeItem('selectedImages');
      localStorage.removeItem('currentImageIndex');
      navigate('/page-selection');
    }
  };

  const handleExit = () => {
    if (confirm('Are you sure you want to exit? Your progress will be saved.')) {
      navigate('/page-selection');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!currentImage) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Image not found</h2>
          <button
            onClick={() => navigate('/page-selection')}
            className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            Back to Page Selection
          </button>
        </div>
      </div>
    );
  }

  const progress = Math.round(((currentImageIndex + 1) / selectedImages.length) * 100);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Work Session</h1>
              <p className="text-gray-600">
                Image {currentImageIndex + 1} of {selectedImages.length}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Progress: {progress}%
              </div>
              <button
                onClick={handleExit}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Exit Session
              </button>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="pb-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image Display */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">{currentImage.title}</h3>
            <div className="mb-4">
              <img
                src={`${import.meta.env.VITE_API_URL}/userimages/${currentImage.filename}`}
                alt={currentImage.title}
                className="w-full h-auto max-h-96 object-contain border rounded"
              />
            </div>
            {currentImage.description && (
              <p className="text-sm text-gray-600 mb-4">{currentImage.description}</p>
            )}
            <div className="text-xs text-gray-400">
              Uploaded: {new Date(currentImage.uploadedAt).toLocaleDateString()}
            </div>
          </div>

          {/* Work Submission Form */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Submit Your Work</h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                  Work Content
                </label>
                <textarea
                  id="content"
                  rows={12}
                  value={submissionContent}
                  onChange={(e) => setSubmissionContent(e.target.value)}
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter your work content here..."
                  required
                />
              </div>
              
              <div className="flex justify-between space-x-4">
                <button
                  type="button"
                  onClick={handleSkip}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Skip This Image
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Submitting...' : 'Submit & Continue'}
                </button>
              </div>
            </form>
            
            {/* Session Info */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Current Image:</span>
                  <span>{currentImageIndex + 1} of {selectedImages.length}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span>Remaining:</span>
                  <span>{selectedImages.length - currentImageIndex - 1} images</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default WorkSession;
